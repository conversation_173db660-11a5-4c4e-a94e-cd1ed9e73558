package com.ttb.top.customerdataservice.feign.ttbtouch;

import com.ttb.top.customerdataservice.model.feign.ttbtouch.request.FeignGetCustomerStatusRequest;
import com.ttb.top.customerdataservice.model.feign.ttbtouch.request.OneappMessagingBizFeignRequest;
import com.ttb.top.customerdataservice.model.feign.ttbtouch.response.FeignGetCustomerStatusResponse;
import com.ttb.top.customerdataservice.model.feign.ttbtouch.response.OneappMessagingBizFeignResponse;
import com.ttb.top.library.requestloghelper.configuration.CustomFeignLoggingConfiguration;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(
    name = "${feign.ttbtouch.name}",
    url = "${feign.ttbtouch.url}",
    configuration = CustomFeignLoggingConfiguration.class
)
public interface TtbTouchFeignClient {

    @CircuitBreaker(name = "ttb-touch")
    @PostMapping("${feign.ttbtouch.apply-service.customer-status.endpoint}")
    FeignGetCustomerStatusResponse getCustomerStatus(
        @RequestHeader HttpHeaders header,
        @RequestBody FeignGetCustomerStatusRequest request
    );

    @CircuitBreaker(name = "ttbTouchMessagingBiz")
    @PostMapping("/v1/messaging-biz/messages")
    OneappMessagingBizFeignResponse sendMessagingBiz(@RequestHeader HttpHeaders header,
        @RequestBody OneappMessagingBizFeignRequest request);
}
