package com.ttb.top.customerdataservice.feign.crmapi;

import com.ttb.top.customerdataservice.model.aes.AesEncryptedPayload;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveAllCaseByCustomerRequest;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveAllCaseByCustomerResponse;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveAllCaseByNTBResponse;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveCaseComplainRequest;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveCaseComplainResponse;
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest;
import com.ttb.top.library.crmapihelper.config.CrmApiFeignConfig;
import com.ttb.top.library.crmapihelper.model.CrmResponseModel;
import com.ttb.top.library.crmapihelper.model.DecryptCrmResponseBody;
import com.ttb.top.library.crmapihelper.model.EncryptCrmRequestBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "${feign.crm.name}",
        url = "${feign.crm.url}", configuration = {CrmApiFeignConfig.class})
public interface CrmApiHelperFeignClient {

    @EncryptCrmRequestBody
    @DecryptCrmResponseBody
    @PostMapping("${feign.crm.retrive-all-case-by-customer.endpoint}")
    CrmResponseModel<List<RetrieveAllCaseByCustomerResponse>> retriveAllCaseByCustomer(
            @RequestBody RetrieveAllCaseByCustomerRequest request
    );

    @EncryptCrmRequestBody
    @DecryptCrmResponseBody
    @PostMapping("${feign.crm.retrive-case-complain-by-customer.endpoint}")
    CrmResponseModel<List<RetrieveCaseComplainResponse>> retrieveCaseComplain(
            @RequestBody RetrieveCaseComplainRequest request
    );

    @EncryptCrmRequestBody
    @DecryptCrmResponseBody
    @PostMapping("${feign.crm.retrive-all-case-by-ntb.endpoint}")
    CrmResponseModel<RetrieveAllCaseByNTBResponse> retriveAllCaseByNTB(
            @RequestBody HistoricalcaseNTBInquiryRequest request
    );

}
