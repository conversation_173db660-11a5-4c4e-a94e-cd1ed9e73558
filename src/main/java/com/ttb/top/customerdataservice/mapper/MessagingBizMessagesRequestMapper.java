package com.ttb.top.customerdataservice.mapper;

import com.ttb.top.customerdataservice.constants.Constants;
import com.ttb.top.customerdataservice.model.feign.ttbtouch.request.OneappMessagingBizFeignRequest;
import com.ttb.top.customerdataservice.model.request.TtbTouchMessageRequest;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MessagingBizMessagesRequestMapper {

    MessagingBizMessagesRequestMapper INSTANCE = Mappers.getMapper(MessagingBizMessagesRequestMapper.class);

    @Mapping(target = "requestId", expression = "java(mapRequestId())")
    @Mapping(target = "channel", constant = Constants.CHANNEL_TEP)
    OneappMessagingBizFeignRequest mapRequest(TtbTouchMessageRequest ttbTouchMessageRequest);

    default String mapRequestId() {
        return UUID.randomUUID().toString().replace(Constants.HYPHEN, StringUtils.EMPTY);
    }
}
