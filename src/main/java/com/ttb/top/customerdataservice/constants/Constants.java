package com.ttb.top.customerdataservice.constants;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.experimental.UtilityClass;

@UtilityClass
public class Constants {

    /*EKYC Header Value & Values*/
    public static final String EKYC_STATUS_CODE_SUCCESS = "1000";

    /*Object mapper*/
    public static final ObjectMapper OBJECT_MAPPER = JsonMapper.builder()
        .addModule(new JavaTimeModule())
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .build();
    public static final String ERROR = "Error";
    public static final String SYSTEM_TIMEOUT_CODE = "9002";
    public static final String LEGACY_API_FAILED_CODE = "9004";
    public static final String DEFAULT_GENERIC_EXCEPTION_CODE = "9000";

    /* Header Constant Key */
    public static final String APP_ID_A0551 = "A0551";
    public static final String SERVICE_NAME = "service-name";
    public static final String REQUEST_UID = "request-uid";
    public static final String REQUEST_APP_ID = "request-app-id";
    public static final String API_KEY = "API-Key";
    public static final String REQUEST_DATETIME = "request-datetime";
    public static final String ACRONYM = "acronym";

    /* Date Format */
    public static final String TIME_FORMAT_HH_MM_SS = "HH:mm:ss";
    public static final String ISO_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String DATETIME_FORMAT_YYYY_MM_DD = "yyyy-MM-dd'T'HH:mm:ss+07:00";

    /*Service*/
    public static final String SERVICE_NAME_CUSTOMERS_GET = "customers-get";
    public static final String SERVICE_NAME_CUSTOMERS_ADVANCE_SEARCH = "customers-advance-search";

    /*Error code*/
    public static final String ETE_ERROR_CODE_4001 = "4001";
    public static final String ETE_ERROR_CODE_5002 = "5002";

    public static final String REDIS_CUSTOM_CACHE_MANAGER_NAME = "customRedisCacheManager";
    public static final String CUSTOMER_PROFILE_MASTER_CACHE_KEY =
        "customer-360-customer-profile-master";
    public static final String CUSTOMER_360_ANALYTIC_PROFILE =
        "customer-360-analytic-profile";

    public static final String BASE_RM_ID_FORMAT = "0011%s%s";
    public static final String RM_ID_VALUE = "rm-id";
    public static final String CUSTOMER_ID_VALUE = "customer-id";
    public static final String FIRST_LAST_NAME = "FIRST_LAST_NAME";
    public static final String RMID = "RMID";
    public static final String CITIZEN_ID = "CITIZEN_ID";
    public static final String MOBILE_NUMBER = "MOBILE_NUMBER";
    public static final String PASSPORT_ID = "PASSPORT_ID";
    public static final String SEARCH_TYPE_RM_ID = "rm-id";
    public static final String SEARCH_TYPE_CITIZEN_ID = "citizen-id";
    public static final String SEARCH_TYPE_MOBILE_NO = "mobile-no";
    public static final String SEARCH_TYPE_PASSPORT_NO = "passport-no";
    public static final String HYPHEN = "-";
    public static final Double SEARCH_LIMIT_DOUBLE = 50.0;

    /*Card type, Customer type*/
    public static final String CI = "CI";
    public static final String PP = "PP";
    public static final String CUST_TYPE_RETAIL = "P";

    /* Common */
    public static final String CUSTOMER_SEARCH_CONFIG = "customer_search_config";
    public static final String PHONE_TYPE_MOBILE = "M";

    public static final String TIMEZONE = "Asia/Bangkok";
    public static final String TIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";

    public static final String FIELD_CREATED_ON = "created_on";
    private static final String DEFAULT_SORT_BY = Constants.FIELD_CREATED_ON;
    private static final String DEFAULT_SORT_ORDER = "desc";
    /* Historical case*/
    public static final String MASTER_DATA_TYPE_SERVICE_CATEGORY = "serviceCategory";
    public static final String MASTER_DATA_TYPE_SUPPORTED_CHANNEL = "supportedChannel";
    public static final String MASTER_DATA_TYPE_STATUS = "status";
    public static final String MASTER_DATA_TYPE_DATASOURCE = "dataSource";

    /* Request Header */
    public static final String STAFF_ID_HEADER = "STAFF-ID";
    public static final String X_REAL_IP = "x-real-ip";

    /* Product Group Id */
    public static final String PRODUCT_GROUP_ID_LN = "LN";
    public static final String PRODUCT_GROUP_ID_INV = "INV";
    public static final String CUSTOM_APP_API_KEY = "api_key";
    public static final String CHANNEL_TEP = "TEP";

    /* Mongo LOV Historical Case */
    public static final String FIELD_MASTER_DATA_TYPE = "master_data_type";


}
