package com.ttb.top.customerdataservice.service.historicalcase;

import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest;
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseinquiryRequest;
import com.ttb.top.customerdataservice.model.historicalcase.response.HistoricalcaseNTBInquiryResponse;
import com.ttb.top.customerdataservice.model.historicalcase.response.HistoricalcaseinquiryResponse;

public interface HistoricalCaseNTBInquiryService {
    HistoricalcaseNTBInquiryResponse getHistoricalNTBInquiry(
            String staffId,
            HistoricalcaseNTBInquiryRequest request
    );
}
