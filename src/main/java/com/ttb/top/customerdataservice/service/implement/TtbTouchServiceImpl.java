package com.ttb.top.customerdataservice.service.implement;

import com.ttb.top.customerdataservice.constants.Constants;
import com.ttb.top.customerdataservice.feign.ttbtouch.TtbTouchFeignClient;
import com.ttb.top.customerdataservice.helper.feign.HeaderHelper;
import com.ttb.top.customerdataservice.mapper.MessagingBizMessagesRequestMapper;
import com.ttb.top.customerdataservice.model.feign.ttbtouch.request.OneappMessagingBizFeignRequest;
import com.ttb.top.customerdataservice.model.feign.ttbtouch.response.OneappMessagingBizFeignResponse;
import com.ttb.top.customerdataservice.model.request.TtbTouchMessageRequest;
import com.ttb.top.customerdataservice.model.response.TtbTouchMessageResponse;
import com.ttb.top.customerdataservice.service.TtbTouchService;
import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.exceptionmodel.exception.CircuitBreakerException;
import com.ttb.top.library.exceptionmodel.exception.LegacySystemFailureException;
import com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class TtbTouchServiceImpl implements TtbTouchService {

    private final TtbTouchFeignClient ttbTouchFeignClient;
    private final HeaderHelper headerHelper;
    private final HttpHeaders httpHeaders;


    @Override
    public TtbTouchMessageResponse sendMessage(TtbTouchMessageRequest request) {
        validateHeader();
        HttpHeaders headers = headerHelper.ttbTouch("messaging-biz", Constants.DATETIME_FORMAT_YYYY_MM_DD);
        headers.set(HttpHeaderConstant.X_FORWARDED_FOR, getXForwardFor());
        OneappMessagingBizFeignRequest messageBizRequest = MessagingBizMessagesRequestMapper.INSTANCE.mapRequest(
            request);
        try {
            log.info("send message biz POST /v1/messaging-biz/messages");
            OneappMessagingBizFeignResponse messagingBizMessagesResponse = ttbTouchFeignClient.sendMessagingBiz(headers,
                messageBizRequest);
            String description = Optional.ofNullable(messagingBizMessagesResponse)
                .map(OneappMessagingBizFeignResponse::getData).orElse(null);
            return TtbTouchMessageResponse.builder().description(description).build();
        } catch (CallNotPermittedException ex) {
            throw new CircuitBreakerException("open circuit breaker POST /v1/messaging-biz/messages", ex);
        } catch (Exception e) {
            throw new LegacySystemFailureException(e);
        }
    }

    private void validateHeader() {
        if (StringUtils.isBlank(httpHeaders.getFirst(HttpHeaderConstant.X_CORRELATION_ID))) {
            throw new BadRequestException("Header x-correlation-id must not be null/empty");
        }

        if (StringUtils.isBlank(httpHeaders.getFirst(HttpHeaderConstant.X_FORWARDED_FOR))
            && StringUtils.isBlank(httpHeaders.getFirst(Constants.X_REAL_IP))) {
            throw new BadRequestException("Header x-forward-for or x-real-ip must not be null/empty");
        }
    }

    private String getXForwardFor() {
        return StringUtils.isNoneBlank(httpHeaders.getFirst(HttpHeaderConstant.X_FORWARDED_FOR)) ? httpHeaders.getFirst(
            HttpHeaderConstant.X_FORWARDED_FOR) : httpHeaders.getFirst(Constants.X_REAL_IP);
    }
}
