package com.ttb.top.customerdataservice.service.historicalcase.implement;

import com.mongodb.MongoException;
import com.ttb.top.customerdataservice.constants.Constants;
import com.ttb.top.customerdataservice.mapper.historicalcase.HistoricalCaseMapper;
import com.ttb.top.customerdataservice.model.entity.Customer360HistoricalEntity;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveAllCaseByCustomerRequest;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveAllCaseByCustomerResponse;
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest;
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseinquiryRequest;
import com.ttb.top.customerdataservice.model.historicalcase.response.HistoricalcaseNTBInquiryResponse;
import com.ttb.top.customerdataservice.model.historicalcase.response.HistoricalcaseinquiryResponse;
import com.ttb.top.customerdataservice.service.historicalcase.HistoricalCaseInquiryService;
import com.ttb.top.customerdataservice.service.historicalcase.HistoricalCaseNTBInquiryService;
import com.ttb.top.library.crmapihelper.model.CrmResponseModel;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import feign.FeignException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class HistoricalCaseNTBInquiryServiceImpl implements HistoricalCaseNTBInquiryService {

    private final MongoTemplate mongoTemplate;
    private final HistoricalCaseMapper historicalCaseMapper;

    @Override
    public HistoricalcaseNTBInquiryResponse getHistoricalNTBInquiry(String staffId, HistoricalcaseNTBInquiryRequest request) {

        List<HistoricalcaseNTBInquiryResponse> retrieveAllCaseResponse = retriveAllCaseByNTB(request);

        return null;
    }

    private List<HistoricalcaseNTBInquiryResponse> retriveAllCaseByNTB(HistoricalcaseNTBInquiryRequest request) {
        try {
            RetrieveAllCaseByCustomerRequest retrieveAllCaseByCustomerRequest = RetrieveAllCaseByCustomerRequest
                    .builder()
                    .ecId(ecId)
                    .rmId(rmId)
                    .employeeId(staffId)
                    .build();
            CrmResponseModel<List<RetrieveAllCaseByCustomerResponse>> feignResponse = crmApiHelperFeignClient
                    .retriveAllCaseByCustomer(retrieveAllCaseByCustomerRequest);

            if ("0".equals(feignResponse.getStatusCode())
                    && "Data not found".equals(feignResponse.getStatusDescription())) {
                throw new GenericException(ResponseCodeEnum.DATA_NOT_FOUND);
            }

            if (!"0".equals(feignResponse.getStatusCode())) {
                throw new GenericException(ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE);
            }

            return feignResponse.getDataObj();
        } catch (FeignException e) {
            log.error("FeignException error getting customer crm: {}", e.getMessage());
            if (isTimeoutException(e)) {
                log.error("Timeout occurred when calling CRM API: {}", e.getMessage());
                throw new GenericException(ResponseCodeEnum.INTERNAL_SERVER_TIMEOUT_ERROR_CODE);
            }
            if (isNotSuccess(e)) {
                throw new GenericException(ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE);
            }
            throw e;
        } catch (CallNotPermittedException e) {
            log.error("open circuit breaker");
            throw new GenericException(ResponseCodeEnum.CIRCUIT_BREAKER_CODE);
        } catch (GenericException e) {
            log.error("GenericException error: ", e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error: ", e);
            throw new GenericException();
        }
    }

}
