package com.ttb.top.customerdataservice.service.historicalcase.implement;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.MongoException;
import com.ttb.top.customerdataservice.exception.FeignExceptionEnums;
import com.ttb.top.customerdataservice.feign.crmapi.CrmApiHelperFeignClient;
import com.ttb.top.customerdataservice.model.entity.Customer360LovHistoricalEntity;
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveAllCaseByNTBResponse;
import com.ttb.top.customerdataservice.model.feign.ete.dncs.listphone.response.FiegnDncsListPhoneNumberResponse;
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest;
import com.ttb.top.customerdataservice.model.historicalcase.response.HistoricalcaseNTBInquiryResponse;
import com.ttb.top.customerdataservice.service.historicalcase.HistoricalCaseNTBInquiryService;
import com.ttb.top.library.crmapihelper.model.CrmResponseModel;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.BusinessException;
import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import feign.FeignException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ttb.top.customerdataservice.constants.Constants.FIELD_MASTER_DATA_TYPE;
import static com.ttb.top.customerdataservice.constants.Constants.MASTER_DATA_TYPE_DATASOURCE;
import static com.ttb.top.customerdataservice.constants.Constants.MASTER_DATA_TYPE_SERVICE_CATEGORY;
import static com.ttb.top.customerdataservice.constants.Constants.MASTER_DATA_TYPE_STATUS;
import static com.ttb.top.customerdataservice.constants.Constants.OBJECT_MAPPER;

@Slf4j
@Service
@RequiredArgsConstructor
public class HistoricalCaseNTBInquiryServiceImpl implements HistoricalCaseNTBInquiryService {

    @Value("${feign.crm.retrive-all-case-by-ntb.status-color-default}")
    private final String defaultColor;

    private final MongoTemplate mongoTemplate;
    private final CrmApiHelperFeignClient crmApiHelperFeignClient;
    public static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<HistoricalcaseNTBInquiryResponse> getHistoricalNTBInquiry(String staffId, HistoricalcaseNTBInquiryRequest request) {

        List<HistoricalcaseNTBInquiryResponse> retrievalCaseByNTB;
        try {
            RetrieveAllCaseByNTBResponse retrieveAllCaseByNTBResponse = getCrmResponse(request);

            retrievalCaseByNTB = retrievalCaseByNTB(retrieveAllCaseByNTBResponse);

        } catch (FeignException e) {
            log.error("FeignException error getting customer crm: {}", e.getMessage());
//            if (isTimeoutException(e)) {
//                log.error("Timeout occurred when calling CRM API: {}", e.getMessage());
//                throw new GenericException(ResponseCodeEnum.INTERNAL_SERVER_TIMEOUT_ERROR_CODE);
//            }
//            if (isNotSuccess(e)) {
//                throw new GenericException(ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE);
//            }
//            throw e;

            if (e.status() == HttpStatus.BAD_REQUEST.value()
                    || e.status() == HttpStatus.UNAUTHORIZED.value()
                    || e.status() == HttpStatus.FORBIDDEN.value()
                    || e.status() == HttpStatus.NOT_FOUND.value()
                    || e.status() == HttpStatus.INTERNAL_SERVER_ERROR.value()) {

                FiegnDncsListPhoneNumberResponse errorMap;
                try {
                    errorMap = OBJECT_MAPPER.readValue(e.contentUTF8(), FiegnDncsListPhoneNumberResponse.class);
                } catch (JsonProcessingException ex) {
                    throw new GenericException(ex);
                }

                throw new BusinessException(ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE.getCode(),
                        HttpStatus.INTERNAL_SERVER_ERROR, errorMap);
            } else {
                throw eteFeignExceptionHandler.handleFeignException(e, FeignExceptionEnums.class);
            }
        } catch (CallNotPermittedException e) {
            log.error("open circuit breaker");
            throw new GenericException(ResponseCodeEnum.CIRCUIT_BREAKER_CODE);
        } catch (GenericException e) {
            log.error("GenericException error: ", e);
            throw e;
        } catch (MongoException | DataAccessException ex) {
            log.error("Database error: ", ex);
            throw new DatabaseErrorException();
        } catch (Exception e) {
            log.error("Unexpected error: ", e);
            throw new GenericException();
        }

        return retrievalCaseByNTB;
    }

    private List<HistoricalcaseNTBInquiryResponse> retrievalCaseByNTB(RetrieveAllCaseByNTBResponse request) {
        log.info("retrievalCaseByNTB");
        List<String> type = new ArrayList<>(Arrays.asList(MASTER_DATA_TYPE_SERVICE_CATEGORY,
                MASTER_DATA_TYPE_DATASOURCE, MASTER_DATA_TYPE_STATUS));
        Query query = buildQueryForLOVHistoricalCase(type);
        List<Customer360LovHistoricalEntity> lovHistoricalEntities = mongoTemplate.find(
                query, Customer360LovHistoricalEntity.class
        );

        if (CollectionUtils.isEmpty(lovHistoricalEntities) ) {
            throw new GenericException(ResponseCodeEnum.DATA_NOT_FOUND, "Result not found");
        }

        List<HistoricalcaseNTBInquiryResponse> historicalcaseNTBInquiryResponseList = new ArrayList<>();
          lovHistoricalEntities.forEach(lovHistoricalEntity -> {
              HistoricalcaseNTBInquiryResponse historicalcaseNTBInquiryResponse = HistoricalcaseNTBInquiryResponse.builder()
                      .issueTh(request.getIssueTh())
                      .issueEn(request.getIssueEn())
                      .caseNumber(request.getCaseNumber())
                      .serviceCategoryCode(request.getServiceCategoryCode())
                      .serviceCategoryValueEn(checkMasterValueEn(lovHistoricalEntity.getMasterDataType()
                              , MASTER_DATA_TYPE_SERVICE_CATEGORY
                              , lovHistoricalEntity.getMasterCode()
                              , request.getCaseStatusCode()
                              , lovHistoricalEntity.getMasterValueEn()))
                      .serviceCategoryValueTh(checkMasterValueTh(lovHistoricalEntity.getMasterDataType()
                              , MASTER_DATA_TYPE_SERVICE_CATEGORY
                              , lovHistoricalEntity.getMasterCode()
                              , request.getCaseStatusCode()
                              , lovHistoricalEntity.getMasterValueTh()))
                      .dataSourceCode(request.getDataSourceCode())
                      .dataSourceValueEn(checkMasterValueEn(lovHistoricalEntity.getMasterDataType()
                              , MASTER_DATA_TYPE_DATASOURCE
                              , lovHistoricalEntity.getMasterCode()
                              , request.getCaseStatusCode()
                              , lovHistoricalEntity.getMasterValueEn()))
                      .dataSourceValueTh(checkMasterValueTh(lovHistoricalEntity.getMasterDataType()
                              , MASTER_DATA_TYPE_DATASOURCE
                              , lovHistoricalEntity.getMasterCode()
                              , request.getCaseStatusCode()
                              , lovHistoricalEntity.getMasterValueTh()))
                      .caseStatusCode(request.getCaseStatusCode())
                      .caseStatusValueEn(checkMasterValueEn(lovHistoricalEntity.getMasterDataType()
                              , MASTER_DATA_TYPE_STATUS
                              , lovHistoricalEntity.getMasterCode()
                              , request.getCaseStatusCode()
                              , lovHistoricalEntity.getMasterValueEn()))
                      .caseStatusValueTh(checkMasterValueTh(lovHistoricalEntity.getMasterDataType()
                              , MASTER_DATA_TYPE_STATUS
                              , lovHistoricalEntity.getMasterCode()
                              , request.getCaseStatusCode()
                              , lovHistoricalEntity.getMasterValueTh()))
                      .caseStatusColorCode(defaultColor)
                      .createdOn(request.getCreatedOn())
                      .build();
              historicalcaseNTBInquiryResponseList.add(historicalcaseNTBInquiryResponse);
          });

        return historicalcaseNTBInquiryResponseList;
    }

    private RetrieveAllCaseByNTBResponse getCrmResponse(HistoricalcaseNTBInquiryRequest request) {
        log.info("Calling CRM API - HistoricalcaseNTBInquiryRequest ");
        CrmResponseModel<RetrieveAllCaseByNTBResponse> feignResponse = crmApiHelperFeignClient
                .retriveAllCaseByNTB(request);

        if ("0".equals(feignResponse.getStatusCode())
                && "Data not found".equals(feignResponse.getStatusDescription())) {
            throw new GenericException(ResponseCodeEnum.DATA_NOT_FOUND);
        }

        if (!"0".equals(feignResponse.getStatusCode())) {
            throw new GenericException(ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE);
        }

        return feignResponse.getDataObj();
    }

    private Query buildQueryForLOVHistoricalCase(List<String> request) {
        Query query = new Query();
        query.addCriteria(Criteria.where(FIELD_MASTER_DATA_TYPE).in(request));
        query.with(Sort.by(new Sort.Order(Sort.Direction.ASC, FIELD_MASTER_DATA_TYPE)));

        return query;
    }

    private String checkMasterValueEn(String masterDataType, String service, String masterCode,
                                    String caseStatusCode, String masterValueEn) {

        if (masterDataType.equals(service) && masterCode.equalsIgnoreCase(caseStatusCode)) {
            return masterValueEn;
        }
        return StringUtils.EMPTY;

    }

    private String checkMasterValueTh(String masterDataType, String service, String masterCode,
                                      String caseStatusCode, String masterValueTh) {

        if (masterDataType.equals(service) && masterCode.equalsIgnoreCase(caseStatusCode)) {
            return masterValueTh;
        }
        return StringUtils.EMPTY;

    }

    private boolean isTimeoutException(Exception e) {
        if (e == null || StringUtils.isBlank(e.getMessage())) {
            return false;
        }
        String message = e.getMessage().toLowerCase();
        return message.contains("timed out") || message.contains("timeout");
    }

    private boolean isNotSuccess(Exception e) {
        if (e == null || StringUtils.isBlank(e.getMessage())) {
            return false;
        }
        try {
            JsonNode rootNode = objectMapper.readTree(e.getMessage());
            JsonNode statusCodeNode = rootNode.findPath("statusCode");
            return statusCodeNode != null && "1".equals(statusCodeNode.asText());
        } catch (JsonProcessingException ex) {
            return e.getMessage().contains("\"statusCode\":\"1\"")
                    || e.getMessage().contains("statusCode:1");
        }
    }

}
