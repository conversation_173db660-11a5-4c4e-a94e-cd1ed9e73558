package com.ttb.top.customerdataservice.model.feign.crmapi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RetrieveAllCaseByCustomerResponse {
    private String issueTh;
    private String issueEn;
    private String caseNumber;
    private String serviceCategoryCode;
    private String serviceCategoryValue;
    private Date slaTargetDate;
    private String dataSourceCode;
    private String dataSourceValue;
    private String caseStatusCode;
    private String caseStatusValue;
    private Date closedDate;
    private String ownerEmployeeId;
    private String ownerName;
    private String ownerTeamId;
    private String ownerTeamName;
    private String createdByEmployeeId;
    private String createdByName;
    private String createdByTeamId;
    private String createdByTeamName;
    private Date createdOn;
    private String createdById;
    private Date modifiedOn;
    private String modifiedById;
}