package com.ttb.top.customerdataservice.model.request;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TtbTouchMessageRequest {

    @NotBlank
    private String messageId;
    @NotBlank
    private String crmId;
    private Map<String, String> paramTh;
    private Map<String, String> paramEn;
}
