package com.ttb.top.customerdataservice.model.historicalcase.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricalcaseNTBInquiryResponse {

    private String issueTh;
    private String issueEn;
    private String caseNumber;
    private String serviceCategoryCode;
    private String serviceCategoryValueEn;
    private String serviceCategoryValueTh;
    private String dataSourceCode;
    private String dataSourceValueEn;
    private String dataSourceValueTh;
    private String caseStatusCode;
    private String caseStatusValueEn;
    private String caseStatusValueTh;
    private String caseStatusColorCode;
    private String createdOn;

}
