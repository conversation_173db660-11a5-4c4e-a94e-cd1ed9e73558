package com.ttb.top.customerdataservice.model.feign.ttbtouch.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OneappMessagingBizFeignRequest {

    private String messageId;
    private String channel;
    private String requestId;
    private String crmId;
    private Map<String, String> paramTh;
    private Map<String, String> paramEn;
}
