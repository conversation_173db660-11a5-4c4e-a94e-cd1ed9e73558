package com.ttb.top.customerdataservice.model.historicalcase.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricalcaseNTBInquiryRequest {

    @NotBlank
    private String contractPersonName;

    @NotBlank
    private String contractPhone;
}
