package com.ttb.top.customerdataservice.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ttb.top.customerdataservice.model.aes.AesEncryptedPayload;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.CommonException;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.SecureRandom;

@Slf4j
@Component
@RequiredArgsConstructor
public class AesUtility {

    private static final int IV_LENGTH = 16;
    private static final String CIPHER = "AES/CBC/PKCS5Padding";
    private static final String ALGORITHM = "AES";
    private static final String KEYSTORE_TYPE = "JCEKS";

    private final ResourceLoader resourceLoader;
    private final ObjectMapper objectMapper;

    @Value("${keystore.path}")
    private String keystorePath;
    @Value("${keystore.password}")
    private String keystorePassword;
    @Value("${key.password}")
    private String keyPassword;
    @Value("${alias}")
    private String alias;
    private SecretKey secretKey;

    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    @PostConstruct
    public void initialize() {
        try {
            this.secretKey = loadSecretKey();
            log.info("Get SecretKey Success");
        } catch (Exception e) {
            log.error(e.toString(), e);
            throw new CommonException(ResponseCodeEnum.GENERIC_ERROR_CODE);
        }
    }

    private SecretKey loadSecretKey() {
        try (InputStream is = getKeyStoreInputStream()) {
            KeyStore keystore = KeyStore.getInstance(KEYSTORE_TYPE);
            keystore.load(is, keystorePassword.toCharArray());
            SecretKey key = (SecretKey) keystore.getKey(alias, keyPassword.toCharArray());
            return new SecretKeySpec(key.getEncoded(), ALGORITHM);
        } catch (IOException e) {
            log.error("IO error while loading secret key", e);
            throw new CommonException(ResponseCodeEnum.GENERIC_ERROR_CODE);
        } catch (GeneralSecurityException e) {
            log.error("Security error while loading secret key", e);
            throw new CommonException(ResponseCodeEnum.GENERIC_ERROR_CODE);
        }
    }

    private InputStream getKeyStoreInputStream() throws IOException {
        try {
            return new FileInputStream(keystorePath);
        } catch (FileNotFoundException e) {
            log.warn("Keystore not found in path");
            InputStream resourceStream = getClass().getClassLoader().getResourceAsStream(keystorePath);
            if (resourceStream == null) {
                throw new FileNotFoundException("Keystore not found in file system or resources: " + keystorePath);
            }
            return resourceStream;
        }
    }

    public AesEncryptedPayload encrypt(String data) throws GeneralSecurityException {
        if (!StringUtils.hasText(data)) {
            throw new IllegalArgumentException("Data to encrypt cannot be null or empty");
        }

        try {
            byte[] iv = generateIv();
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance(CIPHER);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            byte[] encryptedData = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
//            return new AesEncryptedPayload(Hex.encodeHexString(encryptedData), Hex.encodeHexString(iv));
            return new AesEncryptedPayload(Hex.encodeHexString(encryptedData), Hex.encodeHexString(iv));

        } catch (Exception e) {
            log.error("Encrypt error", e);
            throw new CommonException(ResponseCodeEnum.GENERIC_ERROR_CODE);
        }
    }

    public String decrypt(AesEncryptedPayload data) {
        String encryptedHex = data.getEncryptedData();
        if (!StringUtils.hasText(encryptedHex)) {
            throw new IllegalArgumentException("Encrypted data cannot be null or empty");
        }

        String ivHex = data.getInitialVector();
        if (!StringUtils.hasText(ivHex)) {
            throw new IllegalArgumentException("IV cannot be null or empty");
        }

        try {
            byte[] iv = hexStringToByteArray(ivHex);
            byte[] encryptedData = hexStringToByteArray(encryptedHex);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance(CIPHER);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            byte[] decryptedData = cipher.doFinal(encryptedData);
            return new String(decryptedData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Decrypt error", e);
            throw new CommonException(ResponseCodeEnum.GENERIC_ERROR_CODE);
        }

    }

    private byte[] generateIv() {
        byte[] iv = new byte[IV_LENGTH];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);
        return iv;
    }

    public <T> AesEncryptedPayload buildAesEncryptedPayload(T input) {
        try {
            return encrypt(objectMapper.writeValueAsString(input));
        } catch (Exception e) {
            throw new CommonException(ResponseCodeEnum.GENERIC_ERROR_CODE, "Encrypt error", e);
        }
    }
}