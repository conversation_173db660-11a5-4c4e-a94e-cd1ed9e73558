package com.ttb.top.customerdataservice.controller.v1.implement;

import com.ttb.top.customerdataservice.controller.v1.HistoricalCaseCrmController;
import com.ttb.top.customerdataservice.controller.v1.HistoricalCaseCrmNTBController;
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalUpsertRequest;
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest;
import com.ttb.top.customerdataservice.service.historicalcase.HistoricalCaseNTBInquiryService;
import com.ttb.top.customerdataservice.service.historicalcase.HistoricalCaseService;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class HistoricalCaseCrmNTBControllerImpl implements HistoricalCaseCrmNTBController {
    private final HistoricalCaseNTBInquiryService historicalCaseNTBInquiryService;

    @Override
    public ResponseModel<Object> historicalNTBInquiry(String staffId, HistoricalcaseNTBInquiryRequest request) {
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry(staffId, request);
        return ResponseModel.success();
    }
}
