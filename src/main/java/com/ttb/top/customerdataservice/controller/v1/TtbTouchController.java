package com.ttb.top.customerdataservice.controller.v1;

import com.ttb.top.customerdataservice.model.request.TtbTouchMessageRequest;
import com.ttb.top.customerdataservice.model.response.TtbTouchMessageResponse;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/ttbtouch")
public interface TtbTouchController {

    @PostMapping("/message")
    ResponseModel<TtbTouchMessageResponse> sendMessage(@Valid @RequestBody TtbTouchMessageRequest request);
}
