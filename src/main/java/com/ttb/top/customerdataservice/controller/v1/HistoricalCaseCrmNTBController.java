package com.ttb.top.customerdataservice.controller.v1;

import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import static com.ttb.top.customerdataservice.constants.Constants.STAFF_ID_HEADER;

@Validated
@EnableLookup
@RequestMapping("/crm/historical-case-ntb")
public interface HistoricalCaseCrmNTBController {

    @PostMapping("/inquiry")
    ResponseModel<Object> historicalNTBInquiry(
            @RequestHeader(STAFF_ID_HEADER) String staffId,
            @RequestBody @Valid HistoricalcaseNTBInquiryRequest request);

}
