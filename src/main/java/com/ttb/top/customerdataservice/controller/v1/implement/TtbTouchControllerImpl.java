package com.ttb.top.customerdataservice.controller.v1.implement;

import com.ttb.top.customerdataservice.controller.v1.TtbTouchController;
import com.ttb.top.customerdataservice.model.request.TtbTouchMessageRequest;
import com.ttb.top.customerdataservice.model.response.TtbTouchMessageResponse;
import com.ttb.top.customerdataservice.service.TtbTouchService;
import com.ttb.top.customerdataservice.utils.LogExecutionTime;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@EnableLookup
@RestController
@RequiredArgsConstructor
public class TtbTouchControllerImpl implements TtbTouchController {

    private final TtbTouchService ttbTouchService;

    @Override
    @LogExecutionTime
    public ResponseModel<TtbTouchMessageResponse> sendMessage(TtbTouchMessageRequest request) {
        return ResponseModel.success(ttbTouchService.sendMessage(request));
    }
}
