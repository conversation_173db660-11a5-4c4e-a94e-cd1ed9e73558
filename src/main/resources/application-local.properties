spring.application.name=customer-data-service
spring.cloud.openfeign.client.config.default.connectTimeout=60000
spring.cloud.openfeign.client.config.default.readTimeout=10000
spring.cloud.openfeign.okhttp.enabled=true
# lookup
lookup.type.name=top.response.status

# redis

spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.ssl.enabled=true
redis.enabled=true

# decrypt-helper
db.encryption.aes.key=R+nddbXc87mAwUD9jvmY+f36pOoenInvEayGxqspuhE=@tZ76MfyqZbcPkNCB3WJ9ow==
decrypt.rsa.private.key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyRrcxivnJosBTSmh4QgQplY9txAIH7FIigAhyiLCDLQtgtT5pD+11xW3RYnZwWioYQmGqkFMzigfKDOkDduvvZhA7m4+DW9ylxqGuKj644zRwzgiJ5U5qgRmHzmycj3dgiDs/7wP8BZhC48DzoMbbziCURkaFze8mUyrKsDYqqta0TYdDritVeL6Ud//D/CfZSZwOfMxy+BRNDJRADPt+msUFeu2ZBpRq8w/lPyV8vQ98fYSFqlV1N1lsiBOGEwY1ppqFZcnGXQ1K2VQvdC5jhHZyzLaor+XZt64X5OX8tsfjXaT83Th/yWuza1awzqveHZcxw7gyizTjosatlPvVAgMBAAECggEAJkumgo/2BGhfpASx2FNmDYDBJLUcMpODOUIDjobqU+NTNFz6oRr4yXm1k2rxQkU8EaYA0ODb3pBiB/cp/sKHABAOoJ9T/sW26i13AbC1dIXp9+lqUCTf6WT+FPw0vJTc8fGRuLQhSPvyrzu5cRwyW3k16mQGNiv8mWD4Kj4cBKH+V1bpUQaRClgp41ZOMVv+g6xeH9Trv41B5JT7Z2ycTvQVvSXSAtCebbfICH1ELbjfADwV8DH2wDbbrew5RxGSPiXBQcFYib9F3yoi80Zc+AsuYcQC0fNCsygkQ0wicMH8vVFKCRWL8mPQ6aUUXvslDws5JqDkUtdrQMpmpUUoaQKBgQDbzTsvNX4XVxJc36cxZtF4e5wMNNEmRXuvvukI7L/E5amBq6Rj0gRvP8KM5LnPzy4OE0mQVBpL92ppuoputYRsbFOV91AWgBAGGoFL8ho40wDLZ4HCU1Fj6Psl9O4+Rh28w8ZdmgWAvh7FGvvsNMaUL/9ewmLjhpp5f+FjYosNVwKBgQDPosjd92xNuxiDB3p5oXbFVrcqvcsBtZiWtfJftjl4SZQcKMjXqvTiY7Pqrz/SXl1ZVKKDC48cU8PrNSzBhcRwZ2IV1s+4rHbb8Dm8IWVyhTNuSn6ftDews/w5Xzgo/PazxzbXLnqq62LMdxXNjVDLAuN7NfzMmkURzFJv2KmYswKBgQCuC4aPzTW42ZOKwvYq4hV/57Ea4T+zpFVaRjtUe9Ml4A0mxnj3KbelN8GfuwV/DbiUIKWhiVcBTDqQ2cr/+u+OwwA0wY5DIsiNbLNxJZWp5Tq91YokC8Fo8XTdC2MTIIYvkH4kY+9zkBfhT4qn8OpFMPRvXlDbhRwQlTgtcDxXJQKBgBoiejf+IaKzDwXHFjJjEWkLXijCFOBVNCycIDLN4/PxBvR4abdDrGkmdYnvnw/ikstgrMfj15KQNJPRcJ23MZ+YU68+B41OH/PVC99TMMq2W1/hfoipjWzvaqrqAk6ecIr2Yz+4ePY0hI4J2zOxOt8isPFcPUKflFwGJMYxNj+jAoGAYblB6i9MIZu3bpk/WPFPHjHmtKN61pJ2fFrzXyDtk3d5S2+FkqywWCLNXkDD16b4yapOgbzVs0/yIUIO6250DihDpVvViddMAngYaQ7DWHUH5lbGRcPF8znc4XWijaYA0RaaLVhTKmr3NSXDxreOKgiNT4JdaFS7rlivFG4HXzU=
encrypt.rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAska3MYr5yaLAU0poeEIEKZWPbcQCB+xSIoAIcoiwgy0LYLU+aQ/tdcVt0WJ2cFoqGEJhqpBTM4oHygzpA3br72YQO5uPg1vcpcahrio+uOM0cM4IieVOaoEZh85snI93YIg7P+8D/AWYQuPA86DG284glEZGhc3vJlMqyrA2KqrWtE2HQ64rVXi+lHf/w/wn2UmcDnzMcvgUTQyUQAz7fprFBXrtmQaUavMP5T8lfL0PfH2EhapVdTdZbIgThhMGNaaahWXJxl0NStlUL3QuY4R2csy2qK/l2beuF+Tl/LbH412k/N04f8lrs2tWsM6r3h2XMcO4Mos046LGrZT71QIDAQAB

#request-loh-helper
masking.config.file=masking-pattern.xml

# ekyc-service (legacy)
legacy.ekyc-service.name=ekyc-service-legacy
legacy.ekyc-service.url=http://ekycsit.tmbbank.local
legacy.ekyc-service.aes.secret-key=pP5xdYXP50XX1tgZuMdLkjm6ws4EGM3YqAzl/NodWZg=

# ETE config
feign.ete.application-id=A0551
ete.url=http://localhost:2443

# ETE common catalog
feign.ete.common.name=common-catalog
feign.ete.common.url=http://localhost:2443

feign.ete.common.kyc-classifies.endpoint=/v1.0/internal/commons/list-of-values/kyc-classifies
feign.ete.common.kyc-classifies.servicename=commons-list-kyc-classifies

feign.ete.customer.get-customer.endpoint=/v3.0/internal/customers/get-customer
feign.ete.customer.get-customer.servicename=customers-get

feign.ete.registered.get-promptpay-mobile.endpoint=/v3.0/internal/registered-services/promptpay/mobile/get-service-status
feign.ete.registered.get-promptpay-mobile.servicename=registered-promptpay-mobile-get-status

feign.ete.registered.get-promptpay-citizen.endpoint=/v3.0/internal/registered-services/promptpay/citizen/get-service-status
feign.ete.registered.get-promptpay-citizen.servicename=registered-promptpay-citizen-get-status

# ETE customer catalog
feign.ete.customer.name=customer-catalog
feign.ete.customer.url=http://localhost:3000
feign.ete.customer.consents.tax.inquiry.servicename=customers-tax-certificate-consent-get
feign.ete.customer.consents.tax.inquiry.endpoint=/v3.0/internal/customers/tax-certificate-consents/get-consents

feign.ete.customer.customers-dncs.endpoint=/v3.0/internal/customers/dncs/get-dncs
feign.ete.customer.customers-dncs.servicename=customers-dncs-get

feign.ete.customer.consents.pdpa.inquiry.endpoint=/v3.0/internal/customers/pdpa/get-consents
feign.ete.customer.consents.pdpa.inquiry.servicename=customer-consents-pdpa

feign.ete.customer.dncs-update-dncs.endpoint=/v3.0/internal/customers/dncs/update-dncs
feign.ete.customer.dncs-update-dncs.servicename=customers-dncs-update
feign.ete.customer.dncs-update-dncs.LastUpdChnl=TEP

# Custom app service config
feign.custom-app.customer.name=custom-app
feign.custom-app.url=https://stmgccadevs1.tmbtest.local
#feign.custom-app.url=http://localhost:3000
feign.custom-app.dnclists-dncListId.endpoint=/api/dnclists/{dncListId}/phonenumbers
feign.custom-app.dnclists-dncListId.servicename=dnclists-dncListId-phonenumbers
feign.custom-app.dnclists-dncListId.api-key=azlzd2J2NG5zdnlidjVjMXpzN2d3aGh6ZXhrbGFpbGw=
feign.custom-app.dnclists-dncListId=ln:17cd0894-3fa6-4d5d-b8e8-bd43b736daf3|inv:d7ddebd7-207a-4f85-904c-24ae9195b666

# EKYC catalog
feign.ekyc.ekyc-service.name=ekyc-service
feign.ekyc.ekyc-service.url=http://localhost:2443
feign.ekyc.ekyc-service.endpoint=/ekyc_service
feign.ekyc.ekyc-service.request-type=get_dipchip

# oneapp-service
feign.ttbtouch.name=ttbtouch
feign.ttbtouch.url=http://localhost:2443
feign.ttbtouch.apply-service.name=apply-service
feign.ttbtouch.apply-service.customer-status.endpoint=/v1/apply-service/customerstatus
feign.ttbtouch.apply-service.customer-status.api-key=kv-secret-ms-a0551-customer-product-api-key-oneapp-dev
feign.ttbtouch.apply-service.customer-status.channel.id=18

# Redis TTL config
cache.customer360.expire-seconds.customer-360-customer-profile-master=28800
cache.customer360.expire-seconds.customer-360-customer-analytic-profile=28800

# postgresql connection
spring.datasource.url=************************************************
spring.datasource.username=topservicecustomerdatausrdev
spring.datasource.password=Pt0Pwd$dEv_24
spring.datasource.hikari.schema=top_customer_data_service
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
logging.level.org.hibernate.orm.jdbc.bind=trace

# Feign Client Logger Level
feign.client.config.default.loggerLevel=FULL

# Circuit breaker
circuitbreaker.instances.list=default,ekyc,common-catalog,eteAdvanceSearch,eteGetCustomer,customer-catalog, custom-app

# Default config circuit breaker
resilience4j.circuitbreaker.enabled=true
resilience4j.circuitbreaker.configs.default.register-health-indicator=true
resilience4j.circuitbreaker.configs.default.sliding-window-type=time_based
resilience4j.circuitbreaker.configs.default.sliding-window-size=100
resilience4j.circuitbreaker.configs.default.minimum-number-of-calls=50
resilience4j.circuitbreaker.configs.default.failure-rate-threshold=50
resilience4j.circuitbreaker.configs.default.slow-call-duration-threshold=12000
resilience4j.circuitbreaker.configs.default.slow-call-rate-threshold=30
resilience4j.circuitbreaker.configs.default.permitted-number-of-calls-in-half-open-state=5
resilience4j.circuitbreaker.configs.default.wait-duration-in-open-state=15000
resilience4j.circuitbreaker.configs.default.record-failure-predicate=com.ttb.top.library.circuitbreakerhelper.predicate.CustomPredicate

# ETE circuit breaker
resilience4j.circuitbreaker.configs.common-catalog.base-config=default
resilience4j.circuitbreaker.configs.common-catalog.enabled=true
resilience4j.circuitbreaker.configs.eteAdvanceSearch.base-config=default
resilience4j.circuitbreaker.configs.eteAdvanceSearch.enabled=true
resilience4j.circuitbreaker.configs.eteGetCustomer.base-config=default
resilience4j.circuitbreaker.configs.eteGetCustomer.enabled=true
resilience4j.circuitbreaker.configs.customer-catalog.base-config=default
resilience4j.circuitbreaker.configs.customer-catalog.enabled=true
resilience4j.circuitbreaker.configs.custom-app.base-config=default
resilience4j.circuitbreaker.configs.custom-app.enabled=true

# Logging Levels
logging.level.com.example=DEBUG
logging.level.feign.client=DEBUG
logging.level.feign=DEBUG

# Mongo
#spring.data.mongodb.uri=********************************************************************************
#spring.data.mongodb.uri=localhost:37017
#spring.data.mongodb.username=liquibaseusr
#spring.data.mongodb.password=Must_Change_M3
#spring.data.mongodb.dbname=liquibase
#spring.data.mongodb.authSource=admin

# Mongo
#spring.data.mongodb.uri=********************************************************************************
spring.data.mongodb.uri=mongodb+srv://mgdb-a0551-dev-pl-0.gwvhy.mongodb.net
spring.data.mongodb.username=topliquibaseusr_dev
spring.data.mongodb.password=Pt0Pwd%24dEv_24
spring.data.mongodb.dbname=topdevdb
spring.data.mongodb.authSource=admin

# Feign Client CRM
feign.crm.name=crm-service
feign.crm.url=http://crmssp-sit1.ttbbank.local

feign.crm.retrive-all-case-by-customer.endpoint=/case-management/case/retrieveAllCaseByCustomer
feign.crm.retrive-all-case-by-customer.servicename=retriveAllCaseByCustomer
feign.crm.retrive-all-case-by-customer.integration-system=TEP
feign.crm.retrive-all-case-by-customer.chunk=50
feign.crm.retrive-all-case-by-customer.status-color-default=#B0BCCB

feign.crm.retrive-case-complain-by-customer.endpoint=/case-management/case/retrieveCaseComplaintByCustomer
feign.crm.retrive-case-complain-by-customer.servicename=retrieveCaseComplainByCustomer
feign.crm.retrive-case-complain-by-customer.integration-system=TEP
feign.crm.retrive-case-complain-by-customer.chunk=50

feign.crm.retrive-all-case-by-ntb.endpoint=/case-management/case/retriveAllCaseByNTB
feign.crm.retrive-all-case-by-ntb.servicename=retriveAllCaseByNTB
feign.crm.retrive-all-case-by-ntb.integration-system=TEP
feign.crm.retrive-all-case-by-ntb.status-color-default=#B0BCCB

# CRM Configuration
env.crm.url=http://localhost
tep.crm.feign.get-token.url=${env.crm.url}
tep.crm.feign.get-token.path=/oauth-authentication/services/oauth2/token
tep.crm.jwt.grant-type=urn:ietf:params:oauth:grant-type:jwt-bearer
tep.crm.jwt.issuer=AF3B0DAF-2B6A-46CA-AC40-BEB21C8143E0
tep.crm.jwt.subject=tepservice
tep.crm.jwt.audience=crmssp-dev1.ttbbank.local
# todo: replace with key vault
tep.crm.jwt.private-key=MIIEpAIBAAKCAQEAsVtH5PW5lIfPZBg99RpObSUd7ewwsNrbEFcB8ka6Ocu+PIpKe/Gx8gAogajq/OZcfQg/qJPgas5wJM4LlHZTUpsrUS5wQ+2K0PAsmUSvku35TeiZwVU4zXPENfNxKaV0FiTRe+WwYzlMg2pLqyQeFuQSVF4kQmjgN/tpDNi7/VB2FibuSuww/lnVUr+70sWTUFeoOPCJDLdfukYiu1vS244MW7fYE/gz1Umn36Gprc4Wbr3wphkzKsXQO1rNV5S2KWligMEAswDtSptESPDUYO8cBYSUvx+Adv3K5VB6AQl9fsceaV2oIhTpbA753KsAaOEBEbvhIfQYcbwxcPNkuQIDAQABAoIBACkX/luFt/87TIljuI9fDgbqD7o9/LYtr45kqpXJX4Leu3BploKLaPCqqrgIPJJmic2KqL1VrizxMjjE6Rf4JqxAOdQwj2Cd3Ik27Q6CRLAlpZswpteN+IBaL39Urca5iGFNHKC5gpoq/5h/e5Lm5nXlWH3l5j7g7/rorNmGKmCx7iexIDXPggndjXmbAcGn8KgXbHF0LvYulqyYIZkgx8Ue7Qw3ySnCEtLrj8YZLUguXDFPVzpwmeQd/5w/dvexTReJKnsbmfqbGPp4NfC/AeMZ7B3aXPfQHdZy7zwJU4gOJZoB/A+5xYZakvM3i1aTYnZ3EWNYJ5ciV0H9UYng1YECgYEA3dbXMxkNCKh5b+IqyV51N/ZZIxNNJzeBh1r2YP0Abo7gvo0m6A6ezQEQxiUXEH6IPr3rzDTEMJW8JkJ0SwZ/hTgTclaCEQCk+hWmqqrFHVX15r9P/RccyC+Kq27DQjaX1Fa5cixcHsD7iYriXXyK0rLdxQxYiZURX2We+4CFHLECgYEAzKrh4Z3w5T+LrOJruKsTgiSZ6jvuiAujT7qnzoj1f9CNGdQ3djljX0sR4DNEhuus4HrL5UKEqgcoDEnN5ENsM+mgfLqFhbxsLNZbne82LHIAHdPYMVIdKQVVY7DfmNcTnJPFWP6l+Ptn0CmOr4ZmU78xE2sSInC/4tWmkRuiKokCgYEAvJo5H6I+qIvDotGgGs+JzOhOZDm2sI7KOu+k5FR1F4m1nYuX9lbnv9cWEb/a2hUZdG43mMeLvNqwub5p7Jc7t2Ezd1QBBL+Ng2kA57yEFSrOAz3HnZ8Ww0NYG+PxccvETazlwSHht+vObuN7DIwj4npbfXm/lEj+GiLlhWGAyiECgYAYPxZLeD6VqhWOAbTpR7sj69ogGcGEdHyAcYh3u1n6LmbI7I81fk77dxVDDk/L/Bea8HE/U8YLrpVb/1JY/HPRIphUrnQrB8t49FweNbyhP1AsOHCgRy4VMgCPsuCKZtB3+Dmk9WYMPRxrWMEColV1Dm1t831OfzT8VntJLZDkoQKBgQCOh37r2NbyWz8v+tdU7BZg2ONl4DyTL3jHLD5lEtQ3tOaO7npUVDTeaXKYs6+JXEg5642j3ZuUIeH29HtXiRsCr1LtMWyJNIQcOy+AAxFdb9mtYgrZ3Q5UU6WF+C/4RwLBRKt5nPw9sUFCmIf6ZQqU9EdaAvs7E/3BAHzGjaxSHg==
# todo: replace with key vault
tep.crm.encryption.aes.key=WG3+YMfeP1pdDYGuJfxzlOUhsYInK1WV2m+MMB/KcfI=
# CRM access token lifetime duration = 15 minutes
local.cache.expire.crm.oauth2.token=900

# redis-cache-helper properties
local.cache.enable=true
# Optional (Default = 3)
tep.crm.jwt.ttl-minutes=3
# Optional (Default = 1)
tep.crm.api.max-retry=1
# Optional (Default = false)
app.api.debug.logging.feign.enable=true
# Optional (Default = true)
masking.enable=true

# keystore
keystore.path=A0551.jceks
keystore.password=rfCGaoLwuDvK
key.password=X76Jn9m6NYvj
alias=secret_key_tep_scs