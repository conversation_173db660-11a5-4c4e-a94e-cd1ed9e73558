package com.ttb.top.customerdataservice.service.historicalcase

import com.mongodb.MongoException
import com.ttb.top.customerdataservice.constants.Constants
import com.ttb.top.customerdataservice.feign.crmapi.CrmApiHelperFeignClient
import com.ttb.top.customerdataservice.model.entity.Customer360LovHistoricalEntity
import com.ttb.top.customerdataservice.model.feign.crmapi.RetrieveAllCaseByNTBResponse
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest
import com.ttb.top.customerdataservice.service.historicalcase.implement.HistoricalCaseNTBInquiryServiceImpl
import com.ttb.top.customerdataservice.utils.MockResponseTest
import com.ttb.top.library.crmapihelper.model.CrmResponseModel
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException
import com.ttb.top.library.exceptionmodel.exception.GenericException
import feign.Request
import groovy.json.JsonOutput
import io.github.resilience4j.circuitbreaker.CallNotPermittedException
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Query
import spock.lang.Specification

class HistoricalCaseCrmNTBInquiryServiceTest extends Specification {

    def mongoTemplate = Mock(MongoTemplate)
    def crmApiHelperFeignClient = Mock(CrmApiHelperFeignClient)
    def defaultColor = "#AAABBB"

    def historicalCaseNTBInquiryService = new HistoricalCaseNTBInquiryServiceImpl(
            defaultColor,
            mongoTemplate,
            crmApiHelperFeignClient
    )

    def "getHistoricalNTBInquiry should return correct response"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def crmResponse = new CrmResponseModel<RetrieveAllCaseByNTBResponse>()
        crmResponse.setStatusCode("0")
        crmResponse.setStatusDescription("Success")
        crmResponse.setDataObj(new RetrieveAllCaseByNTBResponse(
                dataSourceCode: "mock-code-1",
                caseStatusCode: "mock-code-2",
                caseStatusValue: "mock-value-2",
                createdOn: "2023-01-01"
        ))

        def dummyResult = [
                new Customer360LovHistoricalEntity(
                        masterDataType: "dataSource",
                        masterCode: "mock-code-1",
                        masterValueEn: "mock-value-en-1",
                        masterValueTh: "mock-value-th-1"
                ),
                new Customer360LovHistoricalEntity(
                        masterDataType: "status",
                        masterCode: "mock-code-2",
                        masterValueEn: "mock-value-en-2",
                        masterValueTh: "mock-value-th-2"
                )
        ]
        and:
        crmApiHelperFeignClient.retriveAllCaseByNTB( _ as HistoricalcaseNTBInquiryRequest)
                >> crmResponse

        and:
        1 * mongoTemplate.find({ Query q ->
            q.queryObject["master_data_type"]["\$in"] == ["serviceCategory", "dataSource", "status"]
        }, Customer360LovHistoricalEntity) >> dummyResult

        when:
        def response = historicalCaseNTBInquiryService.getHistoricalNTBInquiry("mock-staff-id", request)

        then:
        response.get(0).caseStatusColorCode == "#AAABBB"
    }

    def "should throw GenericException when CRM API returns error status code"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def crmResponse = new CrmResponseModel<RetrieveAllCaseByNTBResponse>()
        crmResponse.setStatusCode("1")
        crmResponse.setStatusDescription("Error")

        crmApiHelperFeignClient.retriveAllCaseByNTB( _ as HistoricalcaseNTBInquiryRequest)
                >> crmResponse

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        def exception = thrown GenericException
        exception.errorCode == ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE.getCode()
    }

    def "should throw GenericException when CRM API returns data not found"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def crmResponse = new CrmResponseModel<RetrieveAllCaseByNTBResponse>()
        crmResponse.setStatusCode("0")
        crmResponse.setStatusDescription("Data not found")

        crmApiHelperFeignClient.retriveAllCaseByNTB(_ as HistoricalcaseNTBInquiryRequest)
                >> crmResponse

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        def exception = thrown GenericException
        exception.errorCode == ResponseCodeEnum.DATA_NOT_FOUND.getCode()
    }

    def "should throw DatabaseErrorException when MongoException occurs"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def crmResponse = new CrmResponseModel<RetrieveAllCaseByNTBResponse>()
        crmResponse.setStatusCode("0")
        crmResponse.setStatusDescription("Success")
        crmResponse.setDataObj(new RetrieveAllCaseByNTBResponse())

        and: 'simulate mongoTemplate.count throws MongoException'
        crmApiHelperFeignClient.retriveAllCaseByNTB(_ as HistoricalcaseNTBInquiryRequest)
                >> crmResponse

        1 * mongoTemplate.find(_, Customer360LovHistoricalEntity) >> { throw new MongoException("Simulated MongoDB failure") }

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        thrown(DatabaseErrorException)
    }

    def "should throw DatabaseErrorException when MongoException occurs during with GenericException"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def crmResponse = new CrmResponseModel<RetrieveAllCaseByNTBResponse>()
        crmResponse.setStatusCode("0")
        crmResponse.setStatusDescription("Success")
        crmResponse.setDataObj(new RetrieveAllCaseByNTBResponse())

        crmApiHelperFeignClient.retriveAllCaseByNTB(_ as HistoricalcaseNTBInquiryRequest)
                >> crmResponse

        1 * mongoTemplate.find(_, Customer360LovHistoricalEntity) >> {
            throw new GenericException("Database error")
        }

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        thrown(GenericException)
    }

    def "should throw DatabaseErrorException when MongoException occurs during getMasterData with exception"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def crmResponse = new CrmResponseModel<RetrieveAllCaseByNTBResponse>()
        crmResponse.setStatusCode("0")
        crmResponse.setStatusDescription("Success")
        crmResponse.setDataObj(new RetrieveAllCaseByNTBResponse())

        crmApiHelperFeignClient.retriveAllCaseByNTB(_ as HistoricalcaseNTBInquiryRequest)
                >> crmResponse

        1 * mongoTemplate.find(_, Customer360LovHistoricalEntity) >> {
            throw new Exception()
        }

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        thrown(Exception)
    }

    def "should throw DatabaseErrorException when MongoException occurs during getMasterData with no data"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def crmResponse = new CrmResponseModel<RetrieveAllCaseByNTBResponse>()
        crmResponse.setStatusCode("0")
        crmResponse.setStatusDescription("Success")
        crmResponse.setDataObj(new RetrieveAllCaseByNTBResponse())

        crmApiHelperFeignClient.retriveAllCaseByNTB(_ as HistoricalcaseNTBInquiryRequest)
                >> crmResponse

        mongoTemplate.find(_ as Query, Customer360LovHistoricalEntity)
                >> []

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        thrown(GenericException)
    }

    def "should Exception get historical case"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request) >> {
            throw new Exception()
        }
        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        thrown(Exception)
    }

//    def "should successfully upsert historical case data"() {
//        given:
//        def staffId = "STAFF001"
//        def request = HistoricalUpsertRequest.builder()
//                .ecId("EC123")
//                .rmId("")
//                .build()
//
//        def caseResponse = RetrieveAllCaseByCustomerResponse.builder()
//                .serviceCategoryCode("SC001")
//                .serviceCategoryValue("Service Category")
//                .dataSourceCode("DS001")
//                .dataSourceValue("Data Source")
//                .caseStatusCode("CS001")
//                .caseStatusValue("Case Status")
//                .build()
//
//        def crmResponse = new CrmResponseModel<List<RetrieveAllCaseByCustomerResponse>>()
//        crmResponse.setStatusCode("0")
//        crmResponse.setStatusDescription("Success")
//        crmResponse.setDataObj(mockHistoricalCaseUpsert())
//
//        def masterData = [
//                new CustomerDataLovHistoricalCase(
//                        masterDataType: "SERVICE_CATEGORY",
//                        masterCode: "SC001",
//                        masterValueTh: "บริการ"
//                ),
//                new CustomerDataLovHistoricalCase(
//                        masterDataType: "SUPPORTED_CHANNEL",
//                        masterCode: "DS001",
//                        masterValueTh: "ช่องทาง"
//                ),
//                new CustomerDataLovHistoricalCase(
//                        masterDataType: "STATUS",
//                        masterCode: "CS001",
//                        masterValueTh: "สถานะ"
//                )
//        ]
//
//        crmApiHelperFeignClient.retriveAllCaseByCustomer(_ as RetrieveAllCaseByCustomerRequest) >> crmResponse
//        customer360LovHistoricalCaseRepository.findByMasterDataTypeIn(_ as List) >> masterData
//
//        when:
//        historicalCaseService.historicalCaseUpsert(staffId, request)
//
//        then:
//        noExceptionThrown()
//    }

//    def "should throw GenericException when Unexpected error "() {
//        given:
//        def request = HistoricalcaseNTBInquiryRequest.builder()
//                .contractPersonName("อดิศร วันดี")
//                .contractPhone("0821234355")
//                .build()
//
//        when:
//        historicalCaseService.upsertCustomer360HistoricalCase(request, _, _)
//
//        then:
//        thrown(GenericException)
//    }
//
//    def "should throw GenericException when Unexpected error "() {
//        given:
//        def request = HistoricalUpsertRequest.builder()
//                .ecId(null)
//                .rmId(null)
//                .build()
//
//        when:
//        historicalCaseService.upsertCustomer360HistoricalCase(request, _, _)
//
//        then:
//        thrown(GenericException)
//    }
//
//    def "should successfully upsert data"() {
//        given:
//        def request = HistoricalUpsertRequest.builder()
//                .ecId("EC456")
//                .rmId("RM789")
//                .build()
//
//        and:
//        def allCases = mockHistoricalCaseUpsert()
//
//        and:
//        def masterData = [new CustomerDataLovHistoricalCase(
//                masterDataType: "dataSource",
//                masterCode: "SCHA00005",
//                masterValueTh: "ลูกค้าสัมพันธ์"
//        ),]
//
//        and:
//        mongoTemplate.upsert(_, _, _) >> null
//
//        when:
//        historicalCaseService.upsertCustomer360HistoricalCase(request, allCases, masterData)
//
//        then:
//        noExceptionThrown()
//    }

    def "should throw timeout"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()


        def responseBody = JsonOutput.toJson([
                "status": ["code": "504", "description": "System timeout"]
        ])

        crmApiHelperFeignClient.retriveAllCaseByNTB( _ as HistoricalcaseNTBInquiryRequest) >> {
            throw MockResponseTest.throwFeignException(
                    504,
                    "Read timed out",
                    null,
                    responseBody,
                    Request.HttpMethod.POST
            )
        }

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        def exception = thrown GenericException
        exception.errorCode == Constants.SYSTEM_TIMEOUT_CODE
    }

    def "should handle FeignException with statusCode 1 correctly"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def responseBody = JsonOutput.toJson([
                "statusCode": "1",
                "statusDescription": "Error occurred in legacy system"
        ])

        crmApiHelperFeignClient.retriveAllCaseByNTB(_ as HistoricalcaseNTBInquiryRequest) >> {
            throw MockResponseTest.createFeignException(
                    500,
                    responseBody,
                    Request.HttpMethod.POST
            )
        }

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        def exception = thrown(GenericException)
        exception.errorCode == ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE.getCode()
    }

    def "should handle CircuitBreakerException"() {
        given:
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()

        def circuitBreakerException = Mock(CallNotPermittedException)


        and: "encryption works but feign client throws circuit breaker exception"
        crmApiHelperFeignClient.retriveAllCaseByNTB(_ as HistoricalcaseNTBInquiryRequest) >> { throw circuitBreakerException }

        when:
        historicalCaseNTBInquiryService.getHistoricalNTBInquiry("STAFF001", request)

        then:
        def exception = thrown(GenericException)
        exception.errorCode == ResponseCodeEnum.CIRCUIT_BREAKER_CODE.code
    }
}