package com.ttb.top.customerdataservice.service

import com.ttb.top.customerdataservice.constants.Constants
import com.ttb.top.customerdataservice.feign.ttbtouch.TtbTouchFeignClient
import com.ttb.top.customerdataservice.helper.feign.HeaderHelper
import com.ttb.top.customerdataservice.model.feign.ttbtouch.request.OneappMessagingBizFeignRequest
import com.ttb.top.customerdataservice.model.feign.ttbtouch.response.OneappMessagingBizFeignResponse
import com.ttb.top.customerdataservice.model.request.TtbTouchMessageRequest
import com.ttb.top.customerdataservice.service.implement.TtbTouchServiceImpl
import com.ttb.top.customerdataservice.utils.CircuitBreakerTest
import com.ttb.top.customerdataservice.utils.MockResponseTest
import com.ttb.top.library.exceptionmodel.exception.BadRequestException
import com.ttb.top.library.exceptionmodel.exception.GenericException
import com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant
import feign.Request
import groovy.json.JsonOutput
import org.instancio.Instancio
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import spock.lang.Specification

class TtbTouchServiceMessageImplTest extends Specification {
    def ttbTouchFeignClient = Mock(TtbTouchFeignClient)
    def headerHelper = Mock(HeaderHelper)
    def httpHeaders = new HttpHeaders()

    def service = new TtbTouchServiceImpl(ttbTouchFeignClient, headerHelper, httpHeaders)

    def "should return BadRequestException when #scenario is null/empty"() {
        given:
        httpHeaders.set(HttpHeaderConstant.X_FORWARDED_FOR, xForwardFor)
        httpHeaders.set(HttpHeaderConstant.X_CORRELATION_ID, xCorrelationId)
        httpHeaders.set(Constants.X_REAL_IP, xRealIp)
        def request = Instancio.create(TtbTouchMessageRequest)

        when:
        service.sendMessage(request)

        then:
        thrown(BadRequestException)
        where:
        scenario                      | xForwardFor     | xCorrelationId                         | xRealIp
        "x-correlation-id"            | "*************" | ""                                     | ""
        "x-forward-for and x-real-ip" | ""              | "A8e9681f-3390-4ilc-85dx-497083390c65" | ""
    }

    def "should throw GenericException 9998 when send message given send messaging biz throw circuit breaker"() {
        given:
        httpHeaders.set(HttpHeaderConstant.X_FORWARDED_FOR, "*************")
        httpHeaders.set(HttpHeaderConstant.X_CORRELATION_ID, "A8e9681f-3390-4ilc-85dx-497083390c65")
        def request = Instancio.create(TtbTouchMessageRequest)
        headerHelper.ttbTouch(_ as String, _ as String) >> new HttpHeaders()

        1 * ttbTouchFeignClient.sendMessagingBiz(_ as HttpHeaders, _ as OneappMessagingBizFeignRequest) >> {
            CircuitBreakerTest.fail()
        }

        when:
        service.sendMessage(request)

        then:
        def exception = thrown(GenericException)
        exception.getErrorCode() == "9998"
    }

    def "should throw GenericException when send message given send messaging biz unsuccessful"() {
        given:
        httpHeaders.set(HttpHeaderConstant.X_FORWARDED_FOR, "*************")
        httpHeaders.set(HttpHeaderConstant.X_CORRELATION_ID, "A8e9681f-3390-4ilc-85dx-497083390c65")
        def request = Instancio.create(TtbTouchMessageRequest)
        headerHelper.ttbTouch(_ as String, _ as String) >> new HttpHeaders()
        def responseBody = JsonOutput.toJson([
                "status": [
                        "code"       : "9000",
                        "header"     : "ไม่สามารถดำเนินการได้",
                        "description": "ทีทีบี ขออภัยในความไม่สะดวก โปรดลองใหม่อีกครั้งภายหลัง"
                ],
                "data"  : null
        ])

        1 * ttbTouchFeignClient.sendMessagingBiz(_ as HttpHeaders, _ as OneappMessagingBizFeignRequest) >> {
            throw MockResponseTest.throwFeignException(
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    HttpStatus.INTERNAL_SERVER_ERROR.reasonPhrase,
                    null,
                    responseBody,
                    Request.HttpMethod.POST
            )
        }

        when:
        service.sendMessage(request)

        then:
        def exception = thrown(GenericException)
        exception.getErrorCode() == "9997"
    }

    def "should return data when get message given send messaging biz successful"() {
        given:
        httpHeaders.set(HttpHeaderConstant.X_FORWARDED_FOR, "*************")
        httpHeaders.set(HttpHeaderConstant.X_CORRELATION_ID, "A8e9681f-3390-4ilc-85dx-497083390c65")
        def request = new TtbTouchMessageRequest(
                messageId: "test_name_1117",
                crmId: "001100000000000000000025534960",
                paramTh: ["name": "ศิริพร", "address": "1111"],
                paramEn: ["name": "Siriporn", "address": "1111"]
        )
        headerHelper.ttbTouch(_ as String, _ as String) >> new HttpHeaders(["x-forward-for": "*************"])

        1 * ttbTouchFeignClient.sendMessagingBiz(_ as HttpHeaders, _ as OneappMessagingBizFeignRequest) >> {
            HttpHeaders httpHeaders1, OneappMessagingBizFeignRequest messagingBizMessagesRequest ->
                verifyAll {
                    messagingBizMessagesRequest.messageId == request.messageId
                    messagingBizMessagesRequest.crmId == request.crmId
                    messagingBizMessagesRequest.paramTh == request.paramTh
                    messagingBizMessagesRequest.paramEn == request.paramEn
                    messagingBizMessagesRequest.channel == "TEP"
                    messagingBizMessagesRequest.requestId != null
                }
                return new OneappMessagingBizFeignResponse(data: $/{$$channel}YYYYMMDD_{$$crm_id}_{$$request_id}/$)
        }

        when:
        def result = service.sendMessage(request)

        then:
        result.description == $/{$$channel}YYYYMMDD_{$$crm_id}_{$$request_id}/$
    }
}
