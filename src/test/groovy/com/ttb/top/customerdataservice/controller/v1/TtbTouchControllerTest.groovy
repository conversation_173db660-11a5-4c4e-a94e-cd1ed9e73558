package com.ttb.top.customerdataservice.controller.v1


import com.ttb.top.customerdataservice.controller.v1.implement.TtbTouchControllerImpl
import com.ttb.top.customerdataservice.model.request.TtbTouchMessageRequest
import com.ttb.top.customerdataservice.model.response.TtbTouchMessageResponse
import com.ttb.top.customerdataservice.service.TtbTouchService
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import org.instancio.Instancio
import spock.lang.Specification

class TtbTouchControllerTest extends Specification {
    def service = Mock(TtbTouchService)
    def ttbTouchControllerImpl = new TtbTouchControllerImpl(service)

    def "should return success when call sendMessage"() {
        given:
        def request = Instancio.create(TtbTouchMessageRequest)
        1 * service.sendMessage(_ as TtbTouchMessageRequest) >> Instancio.create(TtbTouchMessageResponse)

        when:
        def response = ttbTouchControllerImpl.sendMessage(request)

        then:
        response.getStatus().getCode() == ResponseCodeEnum.GENERIC_SUCCESS_CODE.getCode()
    }
}
