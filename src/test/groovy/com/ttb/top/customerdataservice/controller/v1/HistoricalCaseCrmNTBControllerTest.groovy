package com.ttb.top.customerdataservice.controller.v1

import com.ttb.top.customerdataservice.controller.v1.implement.HistoricalCaseCrmNTBControllerImpl
import com.ttb.top.customerdataservice.model.historicalcase.request.HistoricalcaseNTBInquiryRequest
import com.ttb.top.customerdataservice.service.historicalcase.HistoricalCaseNTBInquiryService
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import jakarta.validation.Validation
import org.springframework.aop.framework.ProxyFactory
import org.springframework.validation.beanvalidation.MethodValidationInterceptor
import spock.lang.Specification

class HistoricalCaseCrmNTBControllerTest extends Specification {
    def historicalCaseNTBInquiryService = Mock(HistoricalCaseNTBInquiryService.class)
    def historicalCaseCrmNTBController

    def setup() {
        def validator = Validation.buildDefaultValidatorFactory().getValidator()
        def methodValidationInterceptor = new MethodValidationInterceptor(validator)

        def proxyFactory = new ProxyFactory(new HistoricalCaseCrmNTBControllerImpl(historicalCaseNTBInquiryService))
        proxyFactory.addAdvice(methodValidationInterceptor)
        historicalCaseCrmNTBController = proxyFactory.getProxy() as HistoricalCaseNTBInquiryService
    }

    def "when call HistoricalCase NTB Inquiry then return data success"() {
        def request = HistoricalcaseNTBInquiryRequest.builder()
                .contractPersonName("อดิศร วันดี")
                .contractPhone("0821234355")
                .build()
        when:
        def actual = historicalCaseCrmNTBController.historicalNTBInquiry("EMP001",request)

        then:
        actual.getStatus().getCode() == ResponseCodeEnum.GENERIC_SUCCESS_CODE.getCode()
    }

}
