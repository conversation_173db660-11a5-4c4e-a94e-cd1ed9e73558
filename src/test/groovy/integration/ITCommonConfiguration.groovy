package integration


import com.ttb.top.customerdataservice.CustomerDataServiceApplication
import com.ttb.top.library.lookuphelper.model.LookupResponse
import com.ttb.top.library.lookuphelper.service.LookupService
import groovy.util.logging.Slf4j
import io.restassured.RestAssured
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.util.TestSocketUtils
import spock.lang.Specification

import javax.cache.Caching
import javax.cache.spi.CachingProvider

@Slf4j
@EnableFeignClients
@ActiveProfiles("test")
@SpringBootTest(
        classes = [CustomerDataServiceApplication],
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@DirtiesContext
class ITCommonConfiguration extends Specification {
    protected static final String BASE_URI = "http://localhost"

    @SpringBean
    LookupService lookupService = Stub() {
        def lookupSuccessResponse = new LookupResponse(header: "สำเร็จ", description: "สำเร็จ", scenario: "Generic success")
        def lookupBadRequestResponse = new LookupResponse(header: "ไม่สามารถดำเนินการได้",
                description: "ทีทีบี ขออภัยในความไม่สะดวก โปรดลองใหม่อีกครั้งภายหลัง", scenario: "Bad request")
        def lookupNotFoundRequestResponse = new LookupResponse(header: "ไม่สามารถดำเนินการได้",
                description: "ไม่พบข้อมูล", scenario: "not found")
        def lookupGeneralResponse = new LookupResponse(header: "ไม่สามารถดำเนินการได้",
                description: "ทีทีบี กำลังดำเนินการแก้ไขอย่างเร่งด่วน ขออภัยในความไม่สะดวกครับ", scenario: "Generic error")
        getLookup("top.response.status", "0000", "TH") >> lookupSuccessResponse
        getLookup("top.response.status", "8000", "TH") >> lookupBadRequestResponse
        getLookup("top.response.status", "8100", "TH") >> lookupNotFoundRequestResponse
        getLookup("top.response.status", "9000", "TH") >> lookupGeneralResponse
        getLookup("top.response.status", "9001", "TH") >> lookupGeneralResponse
    }

    static void setupProps(DynamicPropertyRegistry registry) {
        def port = TestSocketUtils.findAvailableTcpPort()
        registry.add("server.port", () -> port)
        RestAssured.baseURI = BASE_URI
        RestAssured.port = port
    }

    def cleanupSpec() {
        CachingProvider provider = Caching.getCachingProvider()
        provider.close()
    }
}
