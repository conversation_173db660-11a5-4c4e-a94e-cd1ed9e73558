package integration

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.ttb.top.customerdataservice.CustomerDataServiceApplication
import com.ttb.top.library.crmapihelper.client.model.crm.response.CrmGetTokenResponse
import com.ttb.top.library.crmapihelper.model.CrmEncryptedBody
import com.ttb.top.library.crmapihelper.service.CrmApiAuthenticationService
import com.ttb.top.library.crmapihelper.service.CrmApiEncryptionService
import com.ttb.top.library.decryption.util.RSAUtils
import com.ttb.top.library.lookuphelper.model.LookupResponse
import com.ttb.top.library.lookuphelper.service.LookupService
import groovy.util.logging.Slf4j
import util.SocketUtils
import io.restassured.RestAssured
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.DynamicPropertyRegistry
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.MongoDBContainer
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName
import spock.lang.Specification

import javax.cache.Caching
import javax.cache.spi.CachingProvider
import java.security.GeneralSecurityException
import java.time.Instant
import java.time.temporal.ChronoUnit

@Slf4j
@DirtiesContext
@EnableFeignClients
@ActiveProfiles("test")
@SpringBootTest(
        classes = [CustomerDataServiceApplication],
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT
)
class ITConfiguration extends Specification {

    protected static final String BASE_URI = "http://localhost"
    private static final String MONGO_USERNAME = "app_user"
    private static final String MONGO_PASSWORD = "app_password"
    private static final String MONGO_DB_NAME = "topdevdb"
    private static final String PG_USERNAME = "sa"
    private static final String PG_PASSWORD = "password"
    private static final String PG_DB_NAME = "topmsdevdb"

    @SpringBean
    LookupService lookupService = Stub() {
        def lookupSuccessResponse = new LookupResponse(header: "สำเร็จ", description: "สำเร็จ", scenario: "Generic success")
        def lookupBadRequestResponse = new LookupResponse(header: "ไม่สามารถดำเนินการได้",
                description: "ทีทีบี ขออภัยในความไม่สะดวก โปรดลองใหม่อีกครั้งภายหลัง", scenario: "Bad request")
        def lookupNotFoundRequestResponse = new LookupResponse(header: "ไม่สามารถดำเนินการได้",
                description: "ไม่พบข้อมูล", scenario: "not found")
        def lookupGeneralResponse = new LookupResponse(header: "ไม่สามารถดำเนินการได้",
                description: "ทีทีบี กำลังดำเนินการแก้ไขอย่างเร่งด่วน ขออภัยในความไม่สะดวกครับ", scenario: "Generic error")
        getLookup("top.response.status", "0000", "TH") >> lookupSuccessResponse
        getLookup("top.response.status", "8000", "TH") >> lookupBadRequestResponse
        getLookup("top.response.status", "8100", "TH") >> lookupNotFoundRequestResponse
        getLookup("top.response.status", "9000", "TH") >> lookupGeneralResponse
        getLookup("top.response.status", "9001", "TH") >> lookupGeneralResponse
    }
    @SpringBean
    RSAUtils rsaUtils = Stub() {
        final String encryptedPrefix = "rsa-encrypted-"
        encryptString(_ as String) >> { args -> encryptedPrefix + args[0] }
        decryptValue(_ as String) >> { args ->
            String cipherText = args[0]
            if (!cipherText.startsWith(encryptedPrefix)) {
                throw new GeneralSecurityException("Invalid RSA cipher text format")
            }
            return cipherText.replace(encryptedPrefix, "")
        }
        decryptValue(null) >> { throw new GeneralSecurityException("Invalid RSA cipher text format") }
    }
    @SpringBean
    CrmApiAuthenticationService crmApiAuthenticationService = Stub() {
        getToken() >> {
            def accessToken = JWT.create().withExpiresAt(Instant.now().plus(15, ChronoUnit.MINUTES)).sign(Algorithm.none())
            return CrmGetTokenResponse.builder().accessToken(accessToken).tokenType("access_token").build()
        }
    }
    @SpringBean
    CrmApiEncryptionService crmApiEncryptionService = Stub() {
        encrypt(_ as byte[]) >> {
            byte[] plain ->
                CrmEncryptedBody.builder().encryptedData(new String(plain)).initialVector("random-iv").build()
        }
        decrypt(_ as CrmEncryptedBody) >> {
            CrmEncryptedBody crmEncryptedBody -> crmEncryptedBody.getEncryptedData().getBytes()
        }
    }
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:7.0")
            .withEnv("MONGO_INITDB_USERNAME", MONGO_USERNAME)
            .withEnv("MONGO_INITDB_PASSWORD", MONGO_PASSWORD)
            .withEnv("MONGO_INITDB_DATABASE", MONGO_DB_NAME)
    static PostgreSQLContainer pgDbContainer = new PostgreSQLContainer<>("postgres:14.12-alpine")
            .withUsername(PG_USERNAME)
            .withPassword(PG_PASSWORD)
            .withDatabaseName(PG_DB_NAME)
    static redisContainer = new GenericContainer(DockerImageName.parse("redis:6.2.6"))
            .withExposedPorts(6379)

    static void setupProps(DynamicPropertyRegistry registry) {
        def port = SocketUtils.getPort()
        registry.add("server.port", () -> port)
        RestAssured.baseURI = BASE_URI
        RestAssured.port = port
        mongoDBContainer.start()
        def mongoInitResult = mongoDBContainer.execInContainer("mongosh",
                "--eval", "use admin",
                "--eval", "\"db.createUser({ user: '${MONGO_USERNAME}', pwd: '${MONGO_PASSWORD}', roles: [{role: 'readWrite', db: '${MONGO_DB_NAME}'}]})\"")
        log.info("Init Mongo DB result: {}", mongoInitResult.toString())

        def mongoUrl = mongoDBContainer.getReplicaSetUrl(MONGO_DB_NAME)
        log.info("Mongo URL: {}", mongoUrl)
        registry.add("spring.data.mongodb.uri", () -> mongoUrl)
        registry.add("spring.data.mongodb.username", () -> MONGO_USERNAME)
        registry.add("spring.data.mongodb.password", () -> MONGO_PASSWORD)
        registry.add("spring.data.mongodb.dbname", () -> MONGO_DB_NAME)

        redisContainer.start()
        log.info("Redis host: {}", redisContainer.getHost())
        log.info("Redis port: {}", redisContainer.getFirstMappedPort())
        registry.add("spring.data.redis.host", () -> redisContainer.getHost())
        registry.add("spring.data.redis.port", () -> redisContainer.getFirstMappedPort())

        pgDbContainer.start()
        def pgUrl = pgDbContainer.getJdbcUrl()
        log.info("PG URL: {}", pgUrl)
        registry.add("spring.datasource.url", () -> pgUrl)
        registry.add("spring.datasource.username", () -> PG_USERNAME)
        registry.add("spring.datasource.password", () -> PG_PASSWORD)
    }

    def cleanupSpec() {
        CachingProvider provider = Caching.getCachingProvider()
        provider.close()
    }

}
