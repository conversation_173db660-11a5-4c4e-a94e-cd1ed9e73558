package integration.api

import com.github.tomakehurst.wiremock.WireMockServer
import com.ttb.top.customerdataservice.model.request.TtbTouchMessageRequest
import com.ttb.top.customerdataservice.model.response.TtbTouchMessageResponse
import com.ttb.top.library.commonmodel.model.ResponseModel
import integration.ITCommonConfiguration
import util.MockDataUtils
import util.RequestUtil
import util.SocketUtils
import io.restassured.RestAssured
import org.instancio.Instancio
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import spock.lang.Shared

import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson
import static com.github.tomakehurst.wiremock.client.WireMock.okJson
import static com.github.tomakehurst.wiremock.client.WireMock.post
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options
import static groovy.json.JsonOutput.toJson

class TtbTouchServiceMessageIT extends ITCommonConfiguration {

    private static final String API_UNDER_TEST = "/v1/customer-data-service/ttbtouch/message"
    private static final String API_MESSAGE_BIZ_MESSAGES = "/v1/messaging-biz/messages"
    private static final ONE_APP_WM_PORT = SocketUtils.getPort()

    @Shared
    WireMockServer ttbWm = new WireMockServer(options().port(ONE_APP_WM_PORT))

    @DynamicPropertySource
    static void dynamicPropertySource(DynamicPropertyRegistry registry) {
        setupProps(registry)
        def ttbUrl = "${BASE_URI}:${ONE_APP_WM_PORT}"
        registry.add("feign.ttbtouch.url", () -> ttbUrl)
    }

    def setupSpec() {
        ttbWm.start()
    }

    def cleanup() {
        ttbWm.resetAll()
    }

    def cleanupSpec() {
        ttbWm.stop()
    }

    def "Should return HTTP 400 Code 8000 when call API given input is invalid"() {
        given:
        def request = Instancio.create(TtbTouchMessageRequest)
        MockDataUtils.setValueToObject(request, testField, testValue)

        when:
        def response = RestAssured.given()
                .headers(RequestUtil.getDefaultHeaders())
                .body(request)
                .post(API_UNDER_TEST)

        then:
        response.statusCode() == 400
        response.getBody().as(ResponseModel).code == "8000"

        where:
        testField   | testValue
        "messageId" | " "
        "crmId"     | " "

    }

    def "Should return HTTP 200 when call API given ttbtouch message API respond successfully"() {
        given:
        def request = [
                messageId: "test_name_1117",
                crmId    : "001100000000000000000025534960",
                paramTh  : ["name": "ศิริพร", "address": "1111"],
                paramEn  : ["name": "Siriporn", "address": "1111"]
        ]

        ttbWm.stubFor(post(urlPathEqualTo(API_MESSAGE_BIZ_MESSAGES))
                .withRequestBody(equalToJson(toJson([
                        "message_id": "test_name_1117",
                        "crm_id"    : "001100000000000000000025534960",
                        "param_th"  : ["name": "ศิริพร", "address": "1111"],
                        "param_en"  : ["name": "Siriporn", "address": "1111"],
                        "channel"   : "TEP"
                ]), true, true))
                .willReturn(okJson(toJson([
                        "status": [
                                "code"       : "0000",
                                "header"     : "",
                                "description": "Success"
                        ],

                        "data"  : $/{$$channel}YYYYMMDD_{$$crm_id}_{$$request_id}/$
                ]))))

        when:
        def response = RestAssured.given()
                .headers(RequestUtil.getDefaultHeaders())
                .body(request)
                .post(API_UNDER_TEST)

        then:
        response.statusCode() == 200
        def body = response.getBody().as(ResponseModel)
        body.getCode() == "0000"
        def responseData = body.dataObj as TtbTouchMessageResponse
        responseData.description == $/{$$channel}YYYYMMDD_{$$crm_id}_{$$request_id}/$
    }
}
