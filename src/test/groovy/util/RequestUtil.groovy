package util

import io.restassured.RestAssured

class RequestUtil {

    static getDefaultHeaders() {
        return getHeaders()
    }

    static getHeaders(String staffId = "90000") {
        return ["Accept"          : "application/json",
                "Accept-Language" : "TH",
                "App-Version"     : "V1",
                "Authorization"   : "Bearer ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
                "Content-type"    : "application/json",
                "Channel"         : "TEP-TABLET",
                "Device-ID"       : "1",
                "Device-Model"    : "postman",
                "Device-nickname" : "iphone",
                "device-os"       : "ios",
                "STAFF-ID"        : staffId,
                "X-Correlation-ID": "2fc5e038-4011-4cdb-9d70-372396c3f0df",
                "x-Location"      : "0,0",
                "X-Forward-For"   : "*************"
        ]
    }

    static postEndpoint(String path, Object requestBody) {
        return RestAssured.given()
                .headers(getDefaultHeaders())
                .body(requestBody)
                .post(path)
    }

    static getEndpoint(String path, Map<String, String> requestParam) {
        return RestAssured.given()
                .headers(getDefaultHeaders())
                .params(requestParam)
                .get(path)
    }

    static getEndpoint(String path) {
        return RestAssured.given()
                .headers(getDefaultHeaders())
                .get(path)
    }

    static patchEndpoint(String path, Object requestBody) {
        return RestAssured.given()
                .headers(getDefaultHeaders())
                .body(requestBody)
                .patch(path)
    }
}
