package util

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import org.apache.commons.lang3.StringUtils
import org.springframework.util.ResourceUtils

import java.util.regex.Pattern

class MockDataUtils {

    private static Pattern ARRAY_PATTERN = Pattern.compile("(.+)\\[(\\d+)]\$")

    static <T> T readObjectFromJson(String s, Class<T> clazz, boolean isJsonSnakeCase = false) {
        def mapper = new ObjectMapper()
        mapper.registerModule(new JavaTimeModule())
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        if (isJsonSnakeCase) {
            mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
        }
        def filePath = StringUtils.join("classpath:", s)
        def file = ResourceUtils.getFile(filePath)
        def result = mapper.readValue(file, clazz)
        return result
    }

    static void setValueToObject(Object obj, String targetField, Object value) {
        def pathSeparatorIndex = targetField.indexOf('.')
        def isLeafNode = pathSeparatorIndex < 0
        if (isLeafNode) {
            def matcher = ARRAY_PATTERN.matcher(targetField)
            def isArray = matcher.matches()
            if (isArray) {
                def arrayIndex = matcher.group(1)
                (obj as List)[arrayIndex] = value
            } else {
                obj[targetField] = value
            }
        } else {
            def currentPath = targetField.substring(0, pathSeparatorIndex)
            def remainPath = targetField.substring(pathSeparatorIndex + 1)
            def matcher = ARRAY_PATTERN.matcher(currentPath)
            def isArray = matcher.matches()
            if (isArray) {
                def field = matcher.group(1)
                def arrayIndex = matcher.group(2) as int
                setValueToObject((obj[field] as List)[arrayIndex], remainPath, value)
            } else {
                setValueToObject(obj[currentPath], remainPath, value)
            }
        }
    }

    private MockDataUtils() {}
}
