buildscript {
    ext {
        springBootVersion = '3.4.3'
        springCloudVersion = '2024.0.0'
        springCloudAzureDependenciesVersion = '5.19.0'
        ttbVersion = '5.0.56-dev.00710'
        gradleDockerVersion = '0.35.0'
        sonarqubeGradlePluginVersion = '4.4.1.3373'
        swaggerGradlePluginVersion = '2.2.16'
        checkstyleToolVersion = '10.15.0'
        jacocoToolVersion = '0.8.11'
    }
    repositories {
        maven {
            url 'https://nexus.tmbbank.local:8081/repository/oneapp'
            credentials {
                username = mavenUser
                password = mavenPassword
            }
        }
        maven {
            url "https://nexus.tmbbank.local:8081/repository/plugins.gradle/"
            credentials {
                username = mavenUser
                password = mavenPassword
            }
        }
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
        classpath "com.palantir.gradle.docker:gradle-docker:${gradleDockerVersion}"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:${sonarqubeGradlePluginVersion}"
        classpath "io.swagger.core.v3:swagger-gradle-plugin:${swaggerGradlePluginVersion}"
    }
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: "io.swagger.core.v3.swagger-gradle-plugin"
apply plugin: 'com.palantir.docker'
apply plugin: 'jacoco'
apply plugin: 'project-report'
apply plugin: "org.sonarqube"
apply plugin: "checkstyle"
apply plugin: "groovy"

group = 'com.ttb.top'
version = '1.0.0'
java {
    sourceCompatibility = JavaVersion.VERSION_17
}

jar {
    enabled = false
    archiveClassifier = ''
}

if (project.hasProperty('projVersion')) {
    project.version = project.projVersion
} else {
    project.version = '1.0.0'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    maven {
        url 'https://nexus.tmbbank.local:8081/repository/oneapp'
        credentials {
            username = mavenUser
            password = mavenPassword
        }
    }
}

springBoot {
    def buildDate = System.getenv("BUILD_TIMESTAMP") ?: new Date().format('yyyy-MM-dd HH:mm:ss')
    def buildNumber = System.getenv("BUILD_NUMBER") ?: 'local'
    buildInfo {
        properties {
            additional = [
                    'number': buildNumber,
                    'time'  : buildDate
            ]
        }
    }
}

ext {
    set('nexusUsername', project.hasProperty('nexusUser') ? project.getProperty('nexusUser') : 'nexusUser')
    set('nexusPassword', project.hasProperty('nexusPassword') ? project.getProperty('nexusPassword') : 'nexusPassword')
}

dependencies {
    // TTB Dependencies for Library
    implementation(platform("com.ttb.top.library:ttb-dependencies-helper:${ttbVersion}"))
    annotationProcessor(platform("com.ttb.top.library:ttb-dependencies-helper:${ttbVersion}"))

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'com.google.code.gson:gson'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'org.ehcache:ehcache'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'io.github.openfeign:feign-okhttp'
    implementation 'com.auth0:java-jwt'
    implementation 'com.jayway.jsonpath:json-path'
    implementation 'io.github.resilience4j:resilience4j-spring-boot3'
    implementation 'io.github.resilience4j:resilience4j-all'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'io.micrometer:micrometer-registry-prometheus'

    // All needed dependency
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // TTB lib
    implementation 'com.ttb.top.library:common-model'
    implementation 'com.ttb.top.library:httpheader-helper'
    implementation 'com.ttb.top.library:exception-model'
    implementation 'com.ttb.top.library:utility-helper'
    implementation 'com.ttb.top.library:lookup-helper'
    implementation 'com.ttb.top.library:request-log-helper'
    implementation 'com.ttb.top.library:validation-helper'
    implementation 'com.ttb.top.library:decrypt-helper'
    implementation 'com.ttb.top.library:redis-cache-helper'
    implementation 'com.ttb.top.library:swagger-helper'
    implementation 'com.ttb.top.library:circuit-breaker-helper'
    implementation 'com.ttb.top.library:crm-api-helper'

    // For decrypt-helper
    implementation 'jakarta.persistence:jakarta.persistence-api'
    implementation 'org.bouncycastle:bcprov-jdk18on:1.73'

    implementation 'org.springframework:spring-context-support'
    testImplementation 'org.instancio:instancio-junit'

    // Mapstruct
    implementation 'org.mapstruct:mapstruct'
    annotationProcessor 'org.mapstruct:mapstruct-processor'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

    // Testing
    testImplementation 'org.spockframework:spock-spring'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.spockframework:spock-core'

    // Integration testing
    testImplementation 'io.rest-assured:rest-assured'
    testImplementation 'org.testcontainers:spock'
    testImplementation 'org.wiremock:wiremock-standalone'
    testImplementation 'org.testcontainers:mongodb'
    testImplementation 'org.testcontainers:postgresql'

    // Wiremock
    testImplementation 'org.wiremock:wiremock-standalone'

    // jpa
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.postgresql:postgresql'

    // CRM Helper
    implementation 'com.github.ben-manes.caffeine:caffeine'

    implementation("org.apache.httpcomponents.client5:httpclient5:5.5")

}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.azure.spring:spring-cloud-azure-dependencies:${springCloudAzureDependenciesVersion}"
    }
}


tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        events "failed"
    }
}

docker {
    name "com.ttb.top/${project.name}:${project.version}"
    dockerfile file('Dockerfile')
    files jar.archiveFile
    buildArgs(['JAR_FILE': "${jar.archiveFileName}"])
}

tasks.getByPath('dockerPrepare').dependsOn('bootJar')
tasks.getByPath('dockerPrepare').dependsOn('jar')
tasks.getByPath('docker').dependsOn('build')

jacoco {
    toolVersion = "${jacocoToolVersion}"
}

jacocoTestReport {
    reports {
        html.required = true
        xml.required = true
        csv.required = false
    }
}

sonar {
    if (System.getProperty("sonar.host.url").equals(null)) {
        properties {
            System.setProperty('sonar.host.url', 'http://localhost:9000')
            System.setProperty('sonar.projectKey', 'customer-data-service')
            System.setProperty('sonar.projectName', 'customer-data-service')
            System.setProperty('sonar.login', project.getProperty('sonarLogin'))
            System.setProperty('sonar.password', project.getProperty('sonarPassword'))
        }
    }
    properties {
        property 'sonar.coverage.exclusions', '**/configuration/**, **/config/**, **/model/**, **/dto/**, **/wsdl/**, **/entity/**, **/utils/**, **/constant/*, **/repository/*, **/exception/*, **/CustomerDataServiceApplication.java'
        property 'sonar.exclusions', '**/configuration/**, **/config/**, **/model/**, **/dto/**, **/wsdl/**, **/entity/**, **/repository/**, **/utils/**'
    }
}

test {
    testLogging.showStandardStreams = true
    finalizedBy "jacocoTestReport", "installLocalGitHook"
}

checkstyle {
    toolVersion = "${checkstyleToolVersion}"
    configFile = file("${rootDir}/config/checkstyle/checkstyle.xml")
    maxWarnings = 0
    maxErrors = 0
}

tasks.withType(Checkstyle) {
    reports {
        xml.required = false
        html.required = true
        sarif.required = true
    }
}

tasks.register("installLocalGitHook", Copy) {
    from new File(rootProject.rootDir, 'scripts/pre-commit')
    into { new File(rootProject.rootDir, '.git/hooks') }
    fileMode 0775

    from new File(rootProject.rootDir, 'scripts/prepare-commit-msg')
    into { new File(rootProject.rootDir, '.git/hooks') }
    fileMode 0775
}
